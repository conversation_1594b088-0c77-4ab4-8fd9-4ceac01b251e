# 🔐 Informations de Connexion - CPA (College Privé Adventiste)

## 📋 Configuration de l'Application

### Base de données
- **Host:** 127.0.0.1
- **Port:** 3306
- **Database:** cpa
- **Username:** cpa
- **Password:** cpa

### Serveur de développement
- **URL:** http://127.0.0.1:8000
- **Page de connexion:** http://127.0.0.1:8000/login

## 👤 Comptes Utilisateurs Disponibles

### 1. Super Admin (Accès Complet)
- **Nom:** Super Admin
- **Email:** `<EMAIL>`
- **Nom d'utilisateur:** `admin`
- **Mot de passe:** `password`
- **Type:** super_admin
- **Niveau:** 1 (Accès complet au système)

### 2. Comptable
- **Nom:** RANAIVONDRAMBOLA Herimino
- **Email:** `<EMAIL>`
- **Nom d'utilisateur:** `Herimino`
- **Mot de passe:** `cpa`
- **Type:** accountant
- **Niveau:** 5 (Gestion comptable)

## 🎯 Types d'Utilisateurs

| Type | Nom | Niveau | Description |
|------|-----|--------|-------------|
| super_admin | Super Admin | 1 | Accès complet à toutes les fonctionnalités |
| admin | Admin | 2 | Administration générale |
| teacher | Teacher | 3 | Gestion des cours et notes |
| parent | Parent | 4 | Consultation des informations élèves |
| accountant | Accountant | 5 | Gestion financière et comptable |

## 🚀 Commandes de Démarrage

```bash
# Démarrer le serveur
"C:\wamp64\bin\php\php8.1.31\php.exe" artisan serve

# Migrations (déjà exécutées)
"C:\wamp64\bin\php\php8.1.31\php.exe" artisan migrate

# Seeders (déjà exécutés)
"C:\wamp64\bin\php\php8.1.31\php.exe" artisan db:seed --class=SettingsTableSeeder
"C:\wamp64\bin\php\php8.1.31\php.exe" artisan db:seed --class=UserTypesTableSeeder
"C:\wamp64\bin\php\php8.1.31\php.exe" artisan db:seed --class=UsersTableSeeder
```

## ⚙️ Configuration Système

- **Nom de l'école:** COLLEGE PRIVE ADVENTISTE AVARATETEZANA
- **Acronyme:** CPA
- **Session actuelle:** 2018-2019
- **Email système:** <EMAIL>

## 📝 Notes Importantes

1. **Mot de passe par défaut Laravel:** `password` (utilisé pour le Super Admin)
2. **Mot de passe personnalisé:** `cpa` (utilisé pour les autres comptes)
3. **Base de données:** Toutes les tables sont créées et peuplées
4. **Serveur:** WAMP64 avec PHP 8.1.31 et MySQL 9.1.0

## 🔧 Corrections Appliquées

- ✅ Version PHP dans composer.json corrigée (^7.2||^8.0)
- ✅ Base de données et utilisateur MySQL créés
- ✅ Méthode `getSetting()` sécurisée contre les valeurs null
- ✅ Seeders exécutés pour peupler les données de base
