
<?php $__env->startSection('page_title', 'Verification'); ?>
<?php $__env->startSection('content'); ?>
    <div class="card">
        <div class="card-header header-elements-inline">
            <h5 class="card-title"><i class="icon-cash2 mr-2"></i>Verification</h5>
            <?php echo Qs::getPanelOptions(); ?>

        </div>

        <div class="card-body">
            <form method="get" action="<?php echo e(route('payments.filter')); ?>">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-md-6 offset-md-3">
                        <div class="row">
                            <div class="col-md-10">
                                <div class="form-group">
                                    <label for="my_class_id" class="col-form-label font-weight-bold">Classe :</label>
                                    <select required id="my_class_id" name="my_class_id" class="form-control select">
                                        <option id="my_class_id" value="">Choisir une Classe</option>
                                        <?php $__currentLoopData = $class; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option name="class" value="<?php echo e($c->id); ?>"><?php echo e($c->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="payments" class="col-form-label font-weight-bold">Motif :</label>
                                    <select required id="payments" name="my_paymets_id" class="form-control select">
                                        <option value="">Choisir une payments</option>

                                            <option name="payments" id="$payments" value="<?php echo e($c->id); ?>"><?php echo e($c->title); ?></option>
                                        
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-2 mt-4">
                                <div class="text-right mt-1">
                                    <button type="submit" class="btn btn-primary">Valider <i class="icon-paperplane ml-2"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        $(document).ready(function(){
            $('#my_class_id').change(function(){
                var classId = $(this).val();
                if(classId){
                    $.ajax({
                        type:"GET",
                        url:"<?php echo e(route('payments.select')); ?>", // Route without parameter
                        data: { class_id: classId }, // Pass class_id as data
                        success:function(res){               
                            if(res){
                                $("#payments").empty();
                                $.each(res,function(key,value){
                                    $("#payments").append('<option value="'+value.id+'">'+value.title+'</option>');
                                });
    
                            }else{
                                $("#payments").empty();
                            }
                        }
                    });
                }else{
                   alert("Class not found");
                }
            });
        });
    </script>

    

    
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\dev\CPA\CPA\resources\views/pages/support_team/payments/verified.blade.php ENDPATH**/ ?>