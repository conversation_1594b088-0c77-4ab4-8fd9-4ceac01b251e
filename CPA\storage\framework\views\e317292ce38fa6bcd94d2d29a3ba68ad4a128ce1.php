
<?php $__env->startSection('page_title', 'Mon tableau de bord'); ?>
<?php $__env->startSection('content'); ?>

    <?php if(Qs::userIsTeamSA()): ?>
       <div class="row">
           <div class="col-sm-6 col-xl-3">
               <div class="card card-body bg-blue-400 has-bg-image">
                   <div class="media">
                       <div class="media-body">
                           <h3 class="mb-0"><?php echo e($users->where('user_type', 'student')->count()); ?></h3>
                           <span class="text-uppercase font-size-xs font-weight-bold">Total  eleves</span>
                       </div>

                       <div class="ml-3 align-self-center">
                           <i class="icon-users4 icon-3x opacity-75"></i>
                       </div>
                   </div>
               </div>
           </div>

           <div class="col-sm-6 col-xl-3">
               <div class="card card-body bg-danger-400 has-bg-image">
                   <div class="media">
                       <div class="media-body">
                           <h3 class="mb-0"><?php echo e($users->where('user_type', 'teacher')->count()); ?></h3>
                           <span class="text-uppercase font-size-xs">Total Enseignants</span>
                       </div>

                       <div class="ml-3 align-self-center">
                           <i class="icon-users2 icon-3x opacity-75"></i>
                       </div>
                   </div>
               </div>
           </div>

           <div class="col-sm-6 col-xl-3">
               <div class="card card-body bg-success-400 has-bg-image">
                   <div class="media">
                       <div class="mr-3 align-self-center">
                           <i class="icon-pointer icon-3x opacity-75"></i>
                       </div>

                       <div class="media-body text-right">
                           <h3 class="mb-0"><?php echo e($users->where('user_type', 'admin')->count()); ?></h3>
                           <span class="text-uppercase font-size-xs">Total Administrateurs</span>
                       </div>
                   </div>
               </div>
           </div>

           <div class="col-sm-6 col-xl-3">
               <div class="card card-body bg-indigo-400 has-bg-image">
                   <div class="media">
                       <div class="mr-3 align-self-center">
                           <i class="icon-user icon-3x opacity-75"></i>
                       </div>

                       <div class="media-body text-right">
                           <h3 class="mb-0"><?php echo e($users->where('user_type', 'parent')->count()); ?></h3>
                           <span class="text-uppercase font-size-xs">Total Parents</span>
                       </div>
                   </div>
               </div>
           </div>
       </div>
       <?php endif; ?>

    
    <div class="card">
        <div class="card-header header-elements-inline">
            <h5 class="card-title">Calendrier</h5>
         <?php echo Qs::getPanelOptions(); ?>

        </div>

        <div class="card-body">
            <div class="fullcalendar-basic"></div>
        </div>
    </div>
    
    <?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\dev\CPA\CPA\resources\views/pages/support_team/dashboard.blade.php ENDPATH**/ ?>