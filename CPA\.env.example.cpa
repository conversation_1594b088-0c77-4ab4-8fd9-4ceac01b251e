# Configuration CPA - College Privé Adventiste
# Copie de sauvegarde des paramètres de connexion

# Application
APP_NAME=CPA
APP_ENV=local
APP_KEY=base64:RqMJ4x8h7dZiU8SX66ZaIvcLC4x6YKdgr6Yj+osHICE=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

# Base de données
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=cpa
DB_USERNAME=cpa
DB_PASSWORD=cpa

# COMPTES UTILISATEURS DISPONIBLES:
# 
# 1. Super Admin (Accès complet)
#    Email: <EMAIL>
#    Username: admin
#    Password: password
#    Type: super_admin
#
# 2. Comptable
#    Email: <EMAIL>
#    Username: Herimino
#    Password: cpa
#    Type: accountant
#
# URL de connexion: http://127.0.0.1:8000/login
#
# Commande pour démarrer le serveur:
# "C:\wamp64\bin\php\php8.1.31\php.exe" artisan serve
